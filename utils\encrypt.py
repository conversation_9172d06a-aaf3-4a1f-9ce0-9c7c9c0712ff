from cryptography.hazmat.primitives import padding
from cryptography.hazmat.primitives.ciphers import Cipher, algorithms, modes
from cryptography.hazmat.backends import default_backend
import base64
from hashlib import md5
import random
from uuid import uuid1

def AES_Encrypt(data):
    key = b"u2oh6Vu^HWe4_AES"  # Convert to bytes
    iv = b"u2oh6Vu^HWe4_AES"  # Convert to bytes
    padder = padding.PKCS7(128).padder()
    padded_data = padder.update(data.encode('utf-8')) + padder.finalize()
    cipher = Cipher(algorithms.AES(key), modes.CBC(iv), backend=default_backend())
    encryptor = cipher.encryptor()
    encrypted_data = encryptor.update(padded_data) + encryptor.finalize()
    enctext = base64.b64encode(encrypted_data).decode('utf-8')
    return enctext
    
def resort(submit_info):
    return {key: submit_info[key] for key in sorted(submit_info.keys())}

def enc(submit_info, submit_enc_value):
    """
    为您的特定场景定制的、完全正确的加密函数。

    :param submit_info: 包含业务参数的字典 (例如您的 parm)。
    :param submit_enc_value: 从 id="submit_enc" 的 input 元素中获取的值。
    :return: 32位小写MD5哈希值。
    """
    
    # 1. 对业务参数字典按键进行字母排序。
    #    这是一种非常标准和常见的签名生成方式，能确保顺序稳定。
    sorted_info = dict(sorted(submit_info.items()))
    
    # 2. 初始化一个列表，用于存放格式化后的字符串
    needed = []

    # 3. 遍历排序后的业务参数，格式化为 "[key=value]"
    for key, value in sorted_info.items():
        # 确保所有值都是字符串类型，与JS行为一致
        needed.append(f"[{key}={str(value)}]")

    # 4. 将从页面获取的 submit_enc_value 按 "[value]" 格式附加到列表末尾
    needed.append(f"[{submit_enc_value}]")
    
    # 5. 将列表中的所有字符串连接成一个长字符串
    seq = ''.join(needed)
    
    logging.info(f"Final string for MD5 hashing: {seq}")
    
    # 6. 计算 MD5 哈希并返回
    return md5(seq.encode("utf-8")).hexdigest()


def generate_captcha_key(timestamp: int):
    captcha_key = md5((str(timestamp) + str(uuid1())).encode("utf-8")).hexdigest()
    encoded_timestamp = md5(
        (str(timestamp) + "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1" + "slide" + captcha_key).encode("utf-8")
    ).hexdigest() + ":" + str(int(timestamp) + 0x493e0)
    return [captcha_key, encoded_timestamp]
